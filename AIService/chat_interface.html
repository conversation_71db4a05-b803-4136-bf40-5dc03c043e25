<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Government Contracting AI Assistant</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f7f7f8;
            height: 100vh;
            display: flex;
        }

        .sidebar {
            width: 260px;
            background: #202123;
            color: white;
            display: flex;
            flex-direction: column;
            border-right: 1px solid #4d4d4f;
        }

        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #4d4d4f;
        }

        .new-chat-btn {
            width: 100%;
            padding: 12px;
            background: #10a37f;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
        }

        .new-chat-btn:hover {
            background: #0d8f6f;
        }

        .threads-list {
            flex: 1;
            overflow-y: auto;
            padding: 10px;
        }

        .thread-item {
            padding: 12px;
            margin: 4px 0;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            color: #ececf1;
            border: 1px solid transparent;
        }

        .thread-item:hover {
            background: #2a2b32;
        }

        .thread-item.active {
            background: #343541;
            border-color: #10a37f;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: white;
        }

        .chat-header {
            padding: 20px;
            border-bottom: 1px solid #e5e5e5;
            background: white;
        }

        .chat-title {
            font-size: 18px;
            font-weight: 600;
            color: #202123;
        }

        .chat-container {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .message {
            display: flex;
            gap: 12px;
            max-width: 800px;
            margin: 0 auto;
            width: 100%;
        }

        .message.user {
            flex-direction: row-reverse;
        }

        .message-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
            flex-shrink: 0;
        }

        .message.user .message-avatar {
            background: #10a37f;
            color: white;
        }

        .message.assistant .message-avatar {
            background: #ab68ff;
            color: white;
        }

        .message-content {
            flex: 1;
            padding: 12px 16px;
            border-radius: 12px;
            line-height: 1.5;
        }

        .message.user .message-content {
            background: #f0f0f0;
            color: #202123;
        }

        .message.assistant .message-content {
            background: #f9f9f9;
            color: #202123;
            border: 1px solid #e5e5e5;
        }

        .input-container {
            padding: 20px;
            border-top: 1px solid #e5e5e5;
            background: white;
        }

        .input-form {
            max-width: 800px;
            margin: 0 auto;
            position: relative;
        }

        .input-wrapper {
            display: flex;
            gap: 10px;
            align-items: flex-end;
        }

        .message-input {
            flex: 1;
            min-height: 44px;
            max-height: 120px;
            padding: 12px 16px;
            border: 1px solid #d1d5db;
            border-radius: 12px;
            resize: none;
            font-family: inherit;
            font-size: 14px;
            line-height: 1.5;
        }

        .message-input:focus {
            outline: none;
            border-color: #10a37f;
        }

        .send-btn {
            padding: 12px 20px;
            background: #10a37f;
            color: white;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
        }

        .send-btn:hover:not(:disabled) {
            background: #0d8f6f;
        }

        .send-btn:disabled {
            background: #d1d5db;
            cursor: not-allowed;
        }

        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
            flex-wrap: wrap;
        }

        .control-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .control-group label {
            font-size: 12px;
            color: #666;
            font-weight: 500;
        }

        .control-group input, .control-group select {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .checkbox-group input[type="checkbox"] {
            width: auto;
        }

        .loading {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #666;
            font-style: italic;
        }

        .loading::after {
            content: '';
            width: 16px;
            height: 16px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #10a37f;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error {
            color: #dc2626;
            background: #fef2f2;
            padding: 12px;
            border-radius: 8px;
            border: 1px solid #fecaca;
            margin: 10px 0;
        }

        .empty-state {
            text-align: center;
            color: #666;
            padding: 40px 20px;
        }

        .empty-state h3 {
            margin-bottom: 8px;
            color: #202123;
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <div class="sidebar-header">
            <button class="new-chat-btn" onclick="startNewChat()">+ New Chat</button>
        </div>
        <div class="threads-list" id="threadsList">
            <!-- Threads will be loaded here -->
        </div>
    </div>

    <div class="main-content">
        <div class="chat-header">
            <div class="chat-title" id="chatTitle">Government Contracting AI Assistant</div>
        </div>

        <div class="chat-container" id="chatContainer">
            <div class="empty-state">
                <h3>Welcome to Government Contracting AI Assistant</h3>
                <p>Start a new conversation to get help with RFPs, contracts, and procurement questions.</p>
            </div>
        </div>

        <div class="input-container">
            <div class="input-form">
                <div class="controls">
                    <div class="control-group">
                        <label>Opportunity ID</label>
                        <input type="text" id="opportunityId" placeholder="e.g., RFP-2024-001" value="TEST-001">
                    </div>
                    <div class="control-group">
                        <label>Tenant ID</label>
                        <input type="text" id="tenantId" placeholder="e.g., acme-corp" value="test-tenant">
                    </div>
                    <div class="control-group">
                        <label>Source</label>
                        <select id="source">
                            <option value="custom">Custom</option>
                            <option value="sam">SAM</option>
                        </select>
                    </div>
                    <div class="control-group">
                        <div class="checkbox-group">
                            <input type="checkbox" id="webSearch">
                            <label for="webSearch">Enable Web Search</label>
                        </div>
                    </div>
                </div>
                <div class="input-wrapper">
                    <textarea 
                        id="messageInput" 
                        class="message-input" 
                        placeholder="Ask about government contracting, RFPs, compliance requirements..."
                        rows="1"
                    ></textarea>
                    <button class="send-btn" id="sendBtn" onclick="sendMessage()">Send</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000';
        let currentThreadId = null;
        let isLoading = false;

        // Auto-resize textarea
        document.getElementById('messageInput').addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 120) + 'px';
        });

        // Send message on Enter (but allow Shift+Enter for new lines)
        document.getElementById('messageInput').addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        async function loadThreads() {
            try {
                const tenantId = document.getElementById('tenantId').value;
                if (!tenantId) return;

                const response = await fetch(`${API_BASE}/chats/threads?tenant_id=${tenantId}&limit=50`);
                const threads = await response.json();

                const threadsList = document.getElementById('threadsList');
                threadsList.innerHTML = '';

                threads.forEach(thread => {
                    const threadElement = document.createElement('div');
                    threadElement.className = 'thread-item';
                    threadElement.textContent = thread.title;
                    threadElement.onclick = () => selectThread(thread.id, thread.title);
                    threadsList.appendChild(threadElement);
                });
            } catch (error) {
                console.error('Error loading threads:', error);
            }
        }

        async function selectThread(threadId, title) {
            try {
                currentThreadId = threadId;
                document.getElementById('chatTitle').textContent = title;

                // Update active thread in sidebar
                document.querySelectorAll('.thread-item').forEach(item => {
                    item.classList.remove('active');
                });
                event.target.classList.add('active');

                // Load thread messages
                const tenantId = document.getElementById('tenantId').value;
                const response = await fetch(`${API_BASE}/chats/threads/${threadId}?tenant_id=${tenantId}`);
                const thread = await response.json();

                const chatContainer = document.getElementById('chatContainer');
                chatContainer.innerHTML = '';

                thread.messages.forEach(message => {
                    addMessageToChat(message.role, message.content);
                });

                chatContainer.scrollTop = chatContainer.scrollHeight;
            } catch (error) {
                console.error('Error loading thread:', error);
                showError('Failed to load thread');
            }
        }

        async function startNewChat() {
            const messageInput = document.getElementById('messageInput');
            const message = messageInput.value.trim();
            
            if (!message) {
                alert('Please enter a message to start a new chat');
                return;
            }

            try {
                setLoading(true);
                
                const requestData = {
                    message: message,
                    opportunity_id: document.getElementById('opportunityId').value,
                    tenant_id: document.getElementById('tenantId').value,
                    source: document.getElementById('source').value,
                    web_search: document.getElementById('webSearch').checked
                };

                const response = await fetch(`${API_BASE}/chats/start`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });

                const result = await response.json();
                
                if (response.ok) {
                    // Clear chat and set new thread
                    currentThreadId = result.thread_id;
                    document.getElementById('chatTitle').textContent = result.title;
                    document.getElementById('chatContainer').innerHTML = '';
                    
                    // Add user message
                    addMessageToChat('user', message);
                    messageInput.value = '';
                    
                    // Now send the message to get AI response
                    await sendMessageToThread(requestData);
                    
                    // Reload threads list
                    await loadThreads();
                } else {
                    showError(result.detail || 'Failed to start new chat');
                }
            } catch (error) {
                console.error('Error starting new chat:', error);
                showError('Failed to start new chat');
            } finally {
                setLoading(false);
            }
        }

        async function sendMessage() {
            const messageInput = document.getElementById('messageInput');
            const message = messageInput.value.trim();
            
            if (!message || isLoading) return;

            if (!currentThreadId) {
                await startNewChat();
                return;
            }

            try {
                setLoading(true);
                
                const requestData = {
                    message: message,
                    thread_id: currentThreadId,
                    opportunity_id: document.getElementById('opportunityId').value,
                    tenant_id: document.getElementById('tenantId').value,
                    source: document.getElementById('source').value,
                    web_search: document.getElementById('webSearch').checked
                };

                // Add user message to chat
                addMessageToChat('user', message);
                messageInput.value = '';

                await sendMessageToThread(requestData);
            } catch (error) {
                console.error('Error sending message:', error);
                showError('Failed to send message');
            } finally {
                setLoading(false);
            }
        }

        async function sendMessageToThread(requestData) {
            try {
                const response = await fetch(`${API_BASE}/chats/ask`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                // Create assistant message container
                const assistantMessageDiv = addMessageToChat('assistant', '');
                const contentDiv = assistantMessageDiv.querySelector('.message-content');
                
                // Add loading indicator
                contentDiv.innerHTML = '<div class="loading">Thinking...</div>';

                // Read the stream
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let assistantResponse = '';

                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    const chunk = decoder.decode(value);
                    assistantResponse += chunk;
                    
                    // Update the message content
                    contentDiv.textContent = assistantResponse;
                    
                    // Auto-scroll to bottom
                    const chatContainer = document.getElementById('chatContainer');
                    chatContainer.scrollTop = chatContainer.scrollHeight;
                }

            } catch (error) {
                console.error('Error in streaming:', error);
                showError('Failed to get response');
            }
        }

        function addMessageToChat(role, content) {
            const chatContainer = document.getElementById('chatContainer');
            
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role}`;
            
            const avatar = document.createElement('div');
            avatar.className = 'message-avatar';
            avatar.textContent = role === 'user' ? 'U' : 'AI';
            
            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';
            contentDiv.textContent = content;
            
            messageDiv.appendChild(avatar);
            messageDiv.appendChild(contentDiv);
            
            chatContainer.appendChild(messageDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;
            
            return messageDiv;
        }

        function setLoading(loading) {
            isLoading = loading;
            const sendBtn = document.getElementById('sendBtn');
            const messageInput = document.getElementById('messageInput');
            
            sendBtn.disabled = loading;
            messageInput.disabled = loading;
            
            if (loading) {
                sendBtn.textContent = 'Sending...';
            } else {
                sendBtn.textContent = 'Send';
            }
        }

        function showError(message) {
            const chatContainer = document.getElementById('chatContainer');
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error';
            errorDiv.textContent = message;
            chatContainer.appendChild(errorDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }

        // Load threads on page load
        window.addEventListener('load', () => {
            loadThreads();
        });

        // Reload threads when tenant ID changes
        document.getElementById('tenantId').addEventListener('change', loadThreads);
    </script>
</body>
</html>
