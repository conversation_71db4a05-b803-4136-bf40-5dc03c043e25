from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Iterator, Optional, List
from services.proposal.chat_service import ChatService
from loguru import logger

# Create router with /chats prefix
router = APIRouter(prefix="/chats", tags=["chat"])

# Pydantic models for request/response
class ChatRequest(BaseModel):
    question: str
    opportunity_id: str
    tenant_id: str
    source: str

class ChatResponse(BaseModel):
    answer: str

# Initialize chat service
chat_service = ChatService()

@router.post("/ask", response_model=ChatResponse)
async def ask_question(request: ChatRequest):
    """
    Ask a question about a government solicitation and get an answer.
    
    Args:
        request: ChatRequest containing question and context parameters
        
    Returns:
        ChatResponse with answer and context
    """
    try:
        logger.info(f"Received /ask request: question='{request.question}', opportunity_id='{request.opportunity_id}', tenant_id='{request.tenant_id}', source='{request.source}'")
        # Validate question
        if not ChatService.validate_question(request.question):
            logger.warning("Invalid question format received in /ask endpoint")
            raise HTTPException(status_code=400, detail="Invalid question format")

        max_chunks_for_context = 5
        
        # Get answer from chat service
        logger.info("Calling chat_service.get_answer (non-streaming)...")
        result: str = await chat_service.get_answer(
            question=request.question,
            opportunity_id=request.opportunity_id,
            tenant_id=request.tenant_id,
            source=request.source,
            streaming=False,
            max_chunks=max_chunks_for_context
        )
        logger.info("Received answer from chat_service.get_answer")
        
        return ChatResponse(answer=result)
        
    except Exception as e:
        logger.error(f"Error in chat ask endpoint: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/ask/stream")
async def ask_question_stream(request: ChatRequest):
    """
    Ask a question and get a streaming response.
    
    Args:
        request: ChatRequest containing question and context parameters
        
    Returns:
        Streaming response chunks
    """
    try:
        logger.info(f"Received /ask/stream request: question='{request.question}', opportunity_id='{request.opportunity_id}', tenant_id='{request.tenant_id}', source='{request.source}'")
        # Validate question
        if not ChatService.validate_question(request.question):
            logger.warning("Invalid question format received in /ask/stream endpoint")
            raise HTTPException(status_code=400, detail="Invalid question format")
        
        max_chunks_for_context = 3

        async def generate_stream():
            try:
                logger.info("Calling chat_service.get_answer (streaming)...")
                # Get streaming response from chat service
                stream = await chat_service.get_answer(
                    question=request.question,
                    opportunity_id=request.opportunity_id,
                    tenant_id=request.tenant_id,
                    source=request.source,
                    streaming=True,
                    max_chunks=max_chunks_for_context
                )
                logger.info("Streaming response received from chat_service.get_answer")
                if isinstance(stream, str):
                    logger.debug("Streaming response is a string, yielding directly")
                    yield f"{stream}"
                else:
                    logger.debug("Streaming response is an iterator, yielding chunks")
                    for chunk in stream:
                        logger.debug(f"Yielding chunk: {chunk}")
                        yield f"{chunk}"
            except Exception as e:
                logger.error(f"Exception in generate_stream: {e}")
                yield "Sorry I could not complete this question"

        logger.info("Returning StreamingResponse for /ask/stream endpoint")
        return StreamingResponse(
            generate_stream(),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Content-Type": "text/event-stream"
            }
        )
    except Exception as e:
        logger.error(f"Error in chat streaming endpoint: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/history/{opportunity_id}")
async def get_conversation_history(
    opportunity_id: str,
    tenant_id: str,
    source: str,
    limit: int = 50
):
    """
    Get conversation history for a specific opportunity.
    
    Args:
        opportunity_id: The opportunity ID
        tenant_id: The tenant ID
        source: The source type
        limit: Maximum number of history items to return
        
    Returns:
        List of conversation history items
    """
    try:
        history = await chat_service.get_conversation_history(
            opportunity_id=opportunity_id,
            tenant_id=tenant_id,
            source=source,
            limit=limit
        )
        
        return {
            "history": history,
            "opportunity_id": opportunity_id,
            "count": len(history)
        }
        
    except Exception as e:
        logger.error(f"Error getting conversation history: {e}")
        raise HTTPException(status_code=500, detail=str(e))