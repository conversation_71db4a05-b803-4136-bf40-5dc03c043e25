from datetime import datetime
from sqlalchemy import Column, String, DateTime, Text, BigInteger, Boolean, ForeignKey, Index, Integer
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID, JSONB
from models.base import Base, BaseModelMixin
import uuid


class ChatThread(Base, BaseModelMixin):
    """
    Chat thread model for managing conversation threads
    """
    __tablename__ = "chat_threads"
    __table_args__ = (
        {"schema": "opportunity"},
        Index("idx_chat_threads_tenant_user", "tenant_id", "user_id"),
        Index("idx_chat_threads_created_date", "created_date"),
        Index("idx_chat_threads_last_activity", "last_activity_date"),
    )
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(String(255), nullable=False)
    user_id = Column(BigInteger, nullable=False)
    title = Column(String(500), nullable=False)
    opportunity_id = Column(String(255), nullable=True)
    source = Column(String(100), nullable=True)
    created_date = Column(DateTime, nullable=False, default=datetime.utcnow)
    last_activity_date = Column(DateTime, nullable=False, default=datetime.utcnow)
    is_archived = Column(Boolean, default=False, nullable=False)
    message_count = Column(Integer, default=0, nullable=False)
    summary = Column(Text, nullable=True)
    metadata = Column(JSONB, nullable=True)
    
    # Relationships
    messages = relationship("ChatMessage", back_populates="thread", cascade="all, delete-orphan")
    summaries = relationship("ChatThreadSummary", back_populates="thread", cascade="all, delete-orphan")


class ChatMessage(Base, BaseModelMixin):
    """
    Individual chat message model
    """
    __tablename__ = "chat_messages"
    __table_args__ = (
        {"schema": "opportunity"},
        Index("idx_chat_messages_thread_id", "thread_id"),
        Index("idx_chat_messages_created_date", "created_date"),
        Index("idx_chat_messages_role", "role"),
    )
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    thread_id = Column(UUID(as_uuid=True), ForeignKey("opportunity.chat_threads.id"), nullable=False)
    role = Column(String(50), nullable=False)  # 'user', 'assistant', 'system'
    content = Column(Text, nullable=False)
    created_date = Column(DateTime, nullable=False, default=datetime.utcnow)
    token_count = Column(Integer, nullable=True)
    context_chunks = Column(JSONB, nullable=True)
    metadata = Column(JSONB, nullable=True)
    
    # Relationships
    thread = relationship("ChatThread", back_populates="messages")


class ChatThreadSummary(Base, BaseModelMixin):
    """
    Thread summary model for managing conversation summaries when threads get large
    """
    __tablename__ = "chat_thread_summaries"
    __table_args__ = (
        {"schema": "opportunity"},
        Index("idx_chat_thread_summaries_thread_id", "thread_id"),
        Index("idx_chat_thread_summaries_created_date", "created_date"),
    )
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    thread_id = Column(UUID(as_uuid=True), ForeignKey("opportunity.chat_threads.id"), nullable=False)
    summary_content = Column(Text, nullable=False)
    message_range_start = Column(Integer, nullable=False)
    message_range_end = Column(Integer, nullable=False)
    created_date = Column(DateTime, nullable=False, default=datetime.utcnow)
    summary_type = Column(String(50), nullable=False, default="auto")  # 'auto', 'manual'
    metadata = Column(JSONB, nullable=True)
    
    # Relationships
    thread = relationship("ChatThread", back_populates="summaries")


class ChatMemoryVector(Base, BaseModelMixin):
    """
    Model for storing vectorized conversation memories in ChromaDB
    """
    __tablename__ = "chat_memory_vectors"
    __table_args__ = (
        {"schema": "opportunity"},
        Index("idx_chat_memory_vectors_thread_id", "thread_id"),
        Index("idx_chat_memory_vectors_tenant_id", "tenant_id"),
        Index("idx_chat_memory_vectors_created_date", "created_date"),
    )
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    thread_id = Column(UUID(as_uuid=True), ForeignKey("opportunity.chat_threads.id"), nullable=False)
    tenant_id = Column(String(255), nullable=False)
    collection_name = Column(String(255), nullable=False)
    vector_id = Column(String(255), nullable=False)
    content_summary = Column(Text, nullable=False)
    message_ids = Column(JSONB, nullable=False)
    created_date = Column(DateTime, nullable=False, default=datetime.utcnow)
    metadata = Column(JSONB, nullable=True)
    
    # Relationships
    thread = relationship("ChatThread")
