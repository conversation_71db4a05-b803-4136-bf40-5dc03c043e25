# ChatGPT-like Interface for Government Contracting AI

A complete web interface for the enhanced chat system that provides a ChatGPT-like experience for government contracting assistance.

## 🚀 Quick Start

1. **Start the FastAPI server:**
   ```bash
   cd AIService
   python -m uvicorn main:app --reload --host 0.0.0.0 --port 8000
   ```

2. **Open the interface:**
   - Open `chat_interface.html` in your web browser
   - Or serve it with a simple HTTP server:
     ```bash
     python -m http.server 8080
     # Then visit http://localhost:8080/chat_interface.html
     ```

## ✨ Features

### **ChatGPT-like Experience**
- **Sidebar with thread list** - View all your conversation threads
- **Real-time streaming responses** - See AI responses as they're generated
- **Auto-generated meaningful titles** - Threads get descriptive titles based on content
- **Thread persistence** - All conversations are saved and can be resumed

### **Government Contracting Focused**
- **Document context integration** - Uses ChromaDB for relevant document chunks
- **Web search capability** - Real-time Google search via Gemini
- **Professional interface** - Designed for government contracting workflows
- **Configurable parameters** - Opportunity ID, tenant ID, source type

### **Advanced Features**
- **Fast streaming responses** - Real-time text generation
- **Thread management** - Create, view, and manage conversation threads
- **Error handling** - Graceful error messages and recovery
- **Responsive design** - Works on desktop and mobile devices

## 🎯 How to Use

### **Starting a New Chat**
1. Fill in the required fields:
   - **Opportunity ID**: e.g., "RFP-2024-001"
   - **Tenant ID**: e.g., "acme-corp" 
   - **Source**: Choose "Custom" or "SAM"
   - **Web Search**: Enable for real-time web search

2. Type your message and click "Send" or press Enter
3. A new thread will be created with an auto-generated title
4. Watch the AI response stream in real-time

### **Continuing Conversations**
1. Click on any thread in the sidebar to resume
2. All previous messages will be loaded
3. Continue the conversation with full context

### **Web Search Mode**
- Enable "Web Search" checkbox for questions that need current information
- Perfect for questions like:
  - "What are the latest AI procurement guidelines?"
  - "Current cybersecurity requirements for government contracts"
  - "Recent changes in federal contracting regulations"

## 🔧 Configuration

### **API Endpoint**
The interface connects to `http://localhost:8000` by default. Update the `API_BASE` variable in the HTML file if your server runs on a different port.

### **Default Values**
- **Opportunity ID**: "TEST-001"
- **Tenant ID**: "test-tenant"
- **Source**: "custom"
- **Web Search**: Disabled

## 📋 API Integration

The interface uses these endpoints:

### **Start New Chat**
```
POST /chats/start
```
Creates a new thread and returns thread info immediately.

### **Send Message**
```
POST /chats/ask
```
Sends a message and streams the AI response.

### **List Threads**
```
GET /chats/threads?tenant_id={tenant_id}
```
Gets all threads for a tenant.

### **Get Thread Details**
```
GET /chats/threads/{thread_id}?tenant_id={tenant_id}
```
Gets a specific thread with all messages.

## 🎨 UI Components

### **Sidebar**
- **New Chat Button**: Start fresh conversations
- **Thread List**: All your conversation threads
- **Active Thread Highlighting**: Shows current conversation

### **Main Chat Area**
- **Chat Header**: Shows current thread title
- **Message History**: All messages with user/AI avatars
- **Streaming Responses**: Real-time AI text generation
- **Auto-scroll**: Automatically scrolls to latest messages

### **Input Area**
- **Configuration Controls**: Opportunity ID, tenant ID, source, web search
- **Message Input**: Multi-line text area with auto-resize
- **Send Button**: Submit messages (disabled during generation)

## 🚨 Error Handling

The interface handles various error scenarios:
- **Network errors**: Shows connection issues
- **API errors**: Displays server error messages
- **Validation errors**: Highlights missing required fields
- **Streaming errors**: Graceful fallback for connection issues

## 🔍 Example Use Cases

### **RFP Analysis**
```
User: "What are the key technical requirements in this RFP?"
AI: [Analyzes document context and provides detailed breakdown]
```

### **Compliance Questions**
```
User: "What cybersecurity standards do we need to meet?"
AI: [Combines document context with current web search results]
```

### **Proposal Writing**
```
User: "Help me write a response to section 3.2 about technical approach"
AI: [Uses document context to provide tailored response]
```

## 🛠️ Troubleshooting

### **Common Issues**

1. **"Failed to start new chat"**
   - Check if the FastAPI server is running
   - Verify the API_BASE URL is correct
   - Ensure all required fields are filled

2. **"No threads loading"**
   - Check tenant_id is provided
   - Verify database connection
   - Check server logs for errors

3. **"Streaming not working"**
   - Ensure modern browser with fetch/streams support
   - Check network connectivity
   - Verify CORS settings if needed

### **Browser Requirements**
- Modern browser with ES6+ support
- Fetch API and ReadableStream support
- JavaScript enabled

## 🎉 Ready to Use!

The interface is now ready to provide a complete ChatGPT-like experience for government contracting assistance. Simply open the HTML file and start chatting!
