from datetime import datetime
from typing import List, Optional, Dict, Any
from uuid import UUID
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete, desc, and_, or_
from sqlalchemy.orm import selectinload

from models.chat_models import Chat<PERSON>hr<PERSON>, ChatMessage, ChatThreadSummary
from schemas.chat_schemas import (
    ThreadCreateRequest, ThreadUpdateRequest, ThreadHistoryResponse,
    ThreadListResponse, ChatMessageResponse, ThreadListRequest
)
from loguru import logger


class ThreadService:
    """Service for managing chat threads and messages"""
    
    @staticmethod
    async def create_thread(
        db: AsyncSession,
        tenant_id: str,
        user_id: int,
        request: ThreadCreateRequest
    ) -> ChatThread:
        """Create a new chat thread"""
        try:
            # Generate title if not provided
            title = request.title or f"Chat {datetime.utcnow().strftime('%Y-%m-%d %H:%M')}"
            
            thread = ChatThread(
                tenant_id=tenant_id,
                user_id=user_id,
                title=title,
                opportunity_id=request.opportunity_id,
                source=request.source,
                metadata={"created_by": "user"}
            )
            
            db.add(thread)
            await db.flush()
            
            # Add initial message if provided
            if request.initial_message:
                initial_msg = ChatMessage(
                    thread_id=thread.id,
                    role="user",
                    content=request.initial_message,
                    metadata={"is_initial": True}
                )
                db.add(initial_msg)
                thread.message_count = 1
                thread.last_activity_date = datetime.utcnow()
            
            await db.commit()
            await db.refresh(thread)
            
            logger.info(f"Created new thread {thread.id} for tenant {tenant_id}")
            return thread
            
        except Exception as e:
            await db.rollback()
            logger.error(f"Error creating thread: {e}")
            raise

    @staticmethod
    async def get_thread(
        db: AsyncSession,
        thread_id: UUID,
        tenant_id: str,
        user_id: int,
        include_messages: bool = True
    ) -> Optional[ChatThread]:
        """Get a specific thread with optional messages"""
        try:
            query = select(ChatThread).where(
                and_(
                    ChatThread.id == thread_id,
                    ChatThread.tenant_id == tenant_id,
                    ChatThread.user_id == user_id
                )
            )
            
            if include_messages:
                query = query.options(
                    selectinload(ChatThread.messages).selectinload(ChatMessage.thread)
                )
            
            result = await db.execute(query)
            thread = result.scalar_one_or_none()
            
            if thread and include_messages:
                # Sort messages by creation date
                thread.messages.sort(key=lambda x: x.created_date)
            
            return thread
            
        except Exception as e:
            logger.error(f"Error getting thread {thread_id}: {e}")
            raise

    @staticmethod
    async def list_threads(
        db: AsyncSession,
        tenant_id: str,
        user_id: int,
        request: ThreadListRequest
    ) -> List[ChatThread]:
        """List threads for a user with filtering and pagination"""
        try:
            query = select(ChatThread).where(
                and_(
                    ChatThread.tenant_id == tenant_id,
                    ChatThread.user_id == user_id
                )
            )
            
            # Apply filters
            if not request.include_archived:
                query = query.where(ChatThread.is_archived == False)
            
            if request.opportunity_id:
                query = query.where(ChatThread.opportunity_id == request.opportunity_id)
            
            if request.source:
                query = query.where(ChatThread.source == request.source)
            
            # Order by last activity
            query = query.order_by(desc(ChatThread.last_activity_date))
            
            # Apply pagination
            query = query.offset(request.offset).limit(request.limit)
            
            result = await db.execute(query)
            threads = result.scalars().all()
            
            return list(threads)
            
        except Exception as e:
            logger.error(f"Error listing threads for tenant {tenant_id}: {e}")
            raise

    @staticmethod
    async def update_thread(
        db: AsyncSession,
        thread_id: UUID,
        tenant_id: str,
        user_id: int,
        request: ThreadUpdateRequest
    ) -> Optional[ChatThread]:
        """Update thread properties"""
        try:
            # First check if thread exists and belongs to user
            thread = await ThreadService.get_thread(
                db, thread_id, tenant_id, user_id, include_messages=False
            )
            
            if not thread:
                return None
            
            # Update fields
            update_data = {}
            if request.title is not None:
                update_data["title"] = request.title
            if request.is_archived is not None:
                update_data["is_archived"] = request.is_archived
            
            if update_data:
                await db.execute(
                    update(ChatThread)
                    .where(ChatThread.id == thread_id)
                    .values(**update_data)
                )
                await db.commit()
                
                # Refresh thread
                await db.refresh(thread)
            
            logger.info(f"Updated thread {thread_id}")
            return thread
            
        except Exception as e:
            await db.rollback()
            logger.error(f"Error updating thread {thread_id}: {e}")
            raise

    @staticmethod
    async def delete_thread(
        db: AsyncSession,
        thread_id: UUID,
        tenant_id: str,
        user_id: int
    ) -> bool:
        """Delete a thread and all its messages"""
        try:
            # First check if thread exists and belongs to user
            thread = await ThreadService.get_thread(
                db, thread_id, tenant_id, user_id, include_messages=False
            )
            
            if not thread:
                return False
            
            # Delete the thread (cascade will handle messages)
            await db.execute(
                delete(ChatThread).where(ChatThread.id == thread_id)
            )
            await db.commit()
            
            logger.info(f"Deleted thread {thread_id}")
            return True
            
        except Exception as e:
            await db.rollback()
            logger.error(f"Error deleting thread {thread_id}: {e}")
            raise

    @staticmethod
    async def add_message(
        db: AsyncSession,
        thread_id: UUID,
        role: str,
        content: str,
        tenant_id: str,
        user_id: int,
        context_chunks: Optional[List[str]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Optional[ChatMessage]:
        """Add a message to a thread"""
        try:
            # Verify thread exists and belongs to user
            thread = await ThreadService.get_thread(
                db, thread_id, tenant_id, user_id, include_messages=False
            )
            
            if not thread:
                return None
            
            message = ChatMessage(
                thread_id=thread_id,
                role=role,
                content=content,
                context_chunks=context_chunks,
                metadata=metadata or {}
            )
            
            db.add(message)
            
            # Update thread activity and message count
            await db.execute(
                update(ChatThread)
                .where(ChatThread.id == thread_id)
                .values(
                    last_activity_date=datetime.utcnow(),
                    message_count=ChatThread.message_count + 1
                )
            )
            
            await db.commit()
            await db.refresh(message)
            
            logger.info(f"Added {role} message to thread {thread_id}")
            return message
            
        except Exception as e:
            await db.rollback()
            logger.error(f"Error adding message to thread {thread_id}: {e}")
            raise

    @staticmethod
    async def get_thread_messages(
        db: AsyncSession,
        thread_id: UUID,
        tenant_id: str,
        user_id: int,
        limit: Optional[int] = None,
        offset: int = 0
    ) -> List[ChatMessage]:
        """Get messages for a thread with pagination"""
        try:
            # Verify thread access
            thread = await ThreadService.get_thread(
                db, thread_id, tenant_id, user_id, include_messages=False
            )
            
            if not thread:
                return []
            
            query = select(ChatMessage).where(
                ChatMessage.thread_id == thread_id
            ).order_by(ChatMessage.created_date)
            
            if limit:
                query = query.offset(offset).limit(limit)
            
            result = await db.execute(query)
            messages = result.scalars().all()
            
            return list(messages)
            
        except Exception as e:
            logger.error(f"Error getting messages for thread {thread_id}: {e}")
            raise
