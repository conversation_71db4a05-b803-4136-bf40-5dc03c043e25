-- Migration script to add chat thread and message tables
-- Run this against your customer database

-- Create chat_threads table
CREATE TABLE IF NOT EXISTS opportunity.chat_threads (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id VARCHAR(255) NOT NULL,
    title VARCHAR(500) NOT NULL,
    opportunity_id VARCHAR(255),
    source VARCHAR(100),
    created_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_activity_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    is_archived BOOLEAN NOT NULL DEFAULT FALSE,
    message_count INTEGER NOT NULL DEFAULT 0,
    summary TEXT,
    metadata JSONB
);

-- Create indexes for chat_threads
CREATE INDEX IF NOT EXISTS idx_chat_threads_tenant_opp ON opportunity.chat_threads(tenant_id, opportunity_id);
CREATE INDEX IF NOT EXISTS idx_chat_threads_created_date ON opportunity.chat_threads(created_date);
CREATE INDEX IF NOT EXISTS idx_chat_threads_last_activity ON opportunity.chat_threads(last_activity_date);

-- Create chat_messages table
CREATE TABLE IF NOT EXISTS opportunity.chat_messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    thread_id UUID NOT NULL,
    role VARCHAR(50) NOT NULL,
    content TEXT NOT NULL,
    created_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    token_count INTEGER,
    context_chunks JSONB,
    metadata JSONB
);

-- Create indexes for chat_messages
CREATE INDEX IF NOT EXISTS idx_chat_messages_thread_id ON opportunity.chat_messages(thread_id);
CREATE INDEX IF NOT EXISTS idx_chat_messages_created_date ON opportunity.chat_messages(created_date);
CREATE INDEX IF NOT EXISTS idx_chat_messages_role ON opportunity.chat_messages(role);

-- Add foreign key constraint (optional, but recommended)
-- ALTER TABLE opportunity.chat_messages 
-- ADD CONSTRAINT fk_chat_messages_thread_id 
-- FOREIGN KEY (thread_id) REFERENCES opportunity.chat_threads(id) ON DELETE CASCADE;
