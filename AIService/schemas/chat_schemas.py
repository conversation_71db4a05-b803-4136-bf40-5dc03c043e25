from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from uuid import UUID


class ChatMessageCreate(BaseModel):
    """Schema for creating a new chat message"""
    content: str = Field(..., min_length=1, max_length=10000, description="Message content")
    opportunity_id: Optional[str] = Field(None, description="Associated opportunity ID")
    source: Optional[str] = Field(None, description="Source type (custom, sam, etc.)")


class ChatMessageResponse(BaseModel):
    """Schema for chat message response"""
    id: UUID
    role: str
    content: str
    created_date: datetime
    token_count: Optional[int] = None
    metadata: Optional[Dict[str, Any]] = None

    class Config:
        from_attributes = True


class ThreadCreateRequest(BaseModel):
    """Schema for creating a new thread"""
    title: Optional[str] = Field(None, max_length=500, description="Thread title")
    opportunity_id: Optional[str] = Field(None, description="Associated opportunity ID")
    source: Optional[str] = Field(None, description="Source type")
    initial_message: Optional[str] = Field(None, description="Initial message to start the thread")


class ThreadUpdateRequest(BaseModel):
    """Schema for updating thread properties"""
    title: Optional[str] = Field(None, max_length=500, description="New thread title")
    is_archived: Optional[bool] = Field(None, description="Archive status")


class ThreadHistoryResponse(BaseModel):
    """Schema for thread history response"""
    id: UUID
    title: str
    opportunity_id: Optional[str] = None
    source: Optional[str] = None
    created_date: datetime
    last_activity_date: datetime
    is_archived: bool
    message_count: int
    summary: Optional[str] = None
    messages: List[ChatMessageResponse] = []
    metadata: Optional[Dict[str, Any]] = None

    class Config:
        from_attributes = True


class ThreadListResponse(BaseModel):
    """Schema for thread list response"""
    id: UUID
    title: str
    opportunity_id: Optional[str] = None
    source: Optional[str] = None
    created_date: datetime
    last_activity_date: datetime
    is_archived: bool
    message_count: int
    summary: Optional[str] = None

    class Config:
        from_attributes = True


class DeleteThreadResponse(BaseModel):
    """Schema for thread deletion response"""
    success: bool
    message: str
    thread_id: UUID


class UpdateThreadTitleResponse(BaseModel):
    """Schema for thread title update response"""
    success: bool
    message: str
    thread_id: UUID
    new_title: str


class ChatStreamResponse(BaseModel):
    """Schema for streaming chat response"""
    thread_id: UUID
    message_id: UUID
    content: str
    is_complete: bool = False
    metadata: Optional[Dict[str, Any]] = None


class ChatRequest(BaseModel):
    """Schema for chat request"""
    message: str = Field(..., min_length=1, max_length=10000)
    thread_id: Optional[UUID] = Field(None, description="Existing thread ID")
    opportunity_id: Optional[str] = Field(None, description="Associated opportunity ID")
    source: Optional[str] = Field(None, description="Source type")
    max_chunks: int = Field(default=5, ge=1, le=20, description="Maximum context chunks to retrieve")


class ChatResponse(BaseModel):
    """Schema for non-streaming chat response"""
    thread_id: UUID
    message_id: UUID
    content: str
    context_used: List[str] = []
    metadata: Optional[Dict[str, Any]] = None


class ThreadSummaryResponse(BaseModel):
    """Schema for thread summary response"""
    id: UUID
    thread_id: UUID
    summary_content: str
    message_range_start: int
    message_range_end: int
    created_date: datetime
    summary_type: str

    class Config:
        from_attributes = True


class ThreadListRequest(BaseModel):
    """Schema for thread list request with filters"""
    limit: int = Field(default=50, ge=1, le=100)
    offset: int = Field(default=0, ge=0)
    include_archived: bool = Field(default=False)
    opportunity_id: Optional[str] = Field(None)
    source: Optional[str] = Field(None)


class ErrorResponse(BaseModel):
    """Schema for error responses"""
    error: str
    message: str
    details: Optional[Dict[str, Any]] = None
